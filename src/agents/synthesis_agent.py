import json
from typing import Dict, Any, Optional
from src.utils.cost_tracker import CostTracker


class SynthesisAgent:
    """
    Generates a comprehensive, human-readable report from reconciled data
    and logs the associated cost.
    """

    def __init__(
        self,
        llm_provider: Any,
        response_templates: Dict[str, str],
        cost_tracker: CostTracker,
    ):
        """
        Initializes the agent with an LLM provider, response templates,
        and a cost tracker.
        """
        self.llm = llm_provider
        self.templates = response_templates
        self.cost_tracker = cost_tracker

    def synthesize(
        self,
        reconciled_data: Dict[str, Any],
        original_query: str,
        complexity: str,
        timeout_seconds: int = 120
    ) -> str:
        """
        Uses an LLM to format the reconciled data into a structured report
        and logs the transaction cost. Includes timeout handling.
        """
        print("  - [SynthesisAgent] Synthesizing final report...")

        template = self.templates.get(
            f"{complexity}_response",
            "Please provide a summary of your findings.",
        )
        data_summary = json.dumps(reconciled_data, indent=2)

        prompt = f"""
        You are a manufacturing data analyst. Your task is to answer the user's query based on the provided data and format the response using the given template.

        **Original Query:**
        {original_query}

        **Reconciled Data from Tools (JSON format):**
        {data_summary}

        **Instructions:**
        1. Analyze the "validated_data" to find the direct answer to the query.
        2. Review the "issues_found" list to identify any data quality problems.
        3. Note the overall "confidence" score.
        4. Populate the response template below with this information in a clear, human-readable format. If data is missing, state that clearly.

        **Response Template:**
        {template}
        """

       # Wrap LLM generation with timeout handling
        try:
            # REMOVED timeout=timeout_seconds from this call
            response = self.llm.generate(
                prompt
            )
        except Exception as e:
            # Simplified the error handling as timeout is no longer a direct parameter
            return f"Synthesis failed: {str(e)}"

        # Log the cost of this synthesis transaction
        self.cost_tracker.log_transaction(
            response["input_tokens"], response["output_tokens"], self.llm.model_name
        )

        # Return the content generated by the LLM
        return response["content"]