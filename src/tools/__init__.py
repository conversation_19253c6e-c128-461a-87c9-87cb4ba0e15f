from .location_query_tool import <PERSON><PERSON><PERSON>y<PERSON>ool
from .machine_log_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .relationship_tool import RelationshipTool
from .worker_data_tool import Worker<PERSON>ataTool
from .document_parser_tool import DocumentParserTool
from .barcode_validator_tool import BarcodeValidatorTool
from .packing_list_parser_tool import PackingListParserTool # NEW

# A dictionary to easily access all tool classes
TOOL_CLASSES = {
    "location_query_tool": LocationQueryTool,
    "machine_log_tool": MachineLogTool,
    "relationship_tool": RelationshipTool,
    "worker_data_tool": WorkerDataTool,
    "document_parser_tool": DocumentParserTool,
    "barcode_validator_tool": BarcodeValidatorTool,
    "packing_list_parser_tool": PackingListParserTool, # NEW
}