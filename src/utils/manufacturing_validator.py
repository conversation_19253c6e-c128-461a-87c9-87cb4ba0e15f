import pandas as pd
import re
from typing import Dict, List


class ManufacturingValidator:
    """
    Provides reusable validation functions for checking manufacturing data
    integrity against predefined rules.
    """

    def __init__(self):
        self.patterns = {
            "worker_rfid": r"^\d{10}$",
            "printer": r"^Printer_\d+$",
            "gear": r"^3DOR\d{5,6}$",
            "order": r"^ORBOX\d+",
            "material": r"^[A-Z]{4}\d{4}$",
        }

    def validate_barcode_formats(self, df: pd.DataFrame, column: str) -> pd.Series:
        """
        Validates a DataFrame column against known barcode patterns.
        Returns a boolean Series indicating compliance.
        """
        # Find the pattern key that matches the column name or content
        pattern_key = None
        if "worker" in column:
            pattern_key = "worker_rfid"
        elif "printer" in column:
            pattern_key = "printer"
        elif "gear" in column:
            pattern_key = "gear"
        elif "order" in column:
            pattern_key = "order"
        elif "material" in column:
            pattern_key = "material"

        # Dynamically determine pattern based on content if no key matches
        if not pattern_key:
            for key, regex in self.patterns.items():
                if df[column].astype(str).str.match(regex).any():
                    pattern_key = key
                    break

        if pattern_key:
            return df[column].astype(str).str.match(self.patterns[pattern_key])

        # Return True for all if no pattern applies
        return pd.Series([True] * len(df), index=df.index)