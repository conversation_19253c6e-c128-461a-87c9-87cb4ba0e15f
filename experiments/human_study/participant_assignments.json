{"P1": [{"task_id": "P1_task_1", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1115", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P1_task_2", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100091 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_3", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1122", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_4", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100056 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P1_task_5", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List PL1121", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P1_task_6", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX0017, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P1_task_7", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q3", "query_string": "For Order ORBOX0015, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P1_task_8", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1011", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_9", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100098 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_10", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00117, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P2": [{"task_id": "P2_task_1", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q2", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P2_task_2", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100017 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P2_task_3", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_4", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100041 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_5", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1116", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_6", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1122", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P2_task_7", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100091 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_8", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00114, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_9", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100012 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P2_task_10", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List PL1121", "dataset_path": "data/experimental_datasets/Q1_dataset"}], "P3": [{"task_id": "P3_task_1", "participant_id": "P3", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P3_task_2", "participant_id": "P3", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100026 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P3_task_3", "participant_id": "P3", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100061 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P3_task_4", "participant_id": "P3", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1117", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P3_task_5", "participant_id": "P3", "complexity": "hard", "quality_condition": "Q2", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P3_task_6", "participant_id": "P3", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P3_task_7", "participant_id": "P3", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List PL1121", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P3_task_8", "participant_id": "P3", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P3_task_9", "participant_id": "P3", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100098 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P3_task_10", "participant_id": "P3", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1012", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P4": [{"task_id": "P4_task_1", "participant_id": "P4", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100095 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P4_task_2", "participant_id": "P4", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX0017, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P4_task_3", "participant_id": "P4", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1019", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P4_task_4", "participant_id": "P4", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1117", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P4_task_5", "participant_id": "P4", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1120", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P4_task_6", "participant_id": "P4", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00119, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P4_task_7", "participant_id": "P4", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100023 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P4_task_8", "participant_id": "P4", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100067 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P4_task_9", "participant_id": "P4", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0018, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P4_task_10", "participant_id": "P4", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1013", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P5": [{"task_id": "P5_task_1", "participant_id": "P5", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100020 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P5_task_2", "participant_id": "P5", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1019", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P5_task_3", "participant_id": "P5", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100058 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P5_task_4", "participant_id": "P5", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1118", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P5_task_5", "participant_id": "P5", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100061 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P5_task_6", "participant_id": "P5", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1119", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P5_task_7", "participant_id": "P5", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX0017, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P5_task_8", "participant_id": "P5", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100068 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P5_task_9", "participant_id": "P5", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00117, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P5_task_10", "participant_id": "P5", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P6": [{"task_id": "P6_task_1", "participant_id": "P6", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100017 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P6_task_2", "participant_id": "P6", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100093 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P6_task_3", "participant_id": "P6", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100064 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P6_task_4", "participant_id": "P6", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00114, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P6_task_5", "participant_id": "P6", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P6_task_6", "participant_id": "P6", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00117, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P6_task_7", "participant_id": "P6", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1013", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P6_task_8", "participant_id": "P6", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1122", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P6_task_9", "participant_id": "P6", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1017", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P6_task_10", "participant_id": "P6", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P7": [{"task_id": "P7_task_1", "participant_id": "P7", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1122", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P7_task_2", "participant_id": "P7", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1115", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P7_task_3", "participant_id": "P7", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100076 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P7_task_4", "participant_id": "P7", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100033 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P7_task_5", "participant_id": "P7", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR10001 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P7_task_6", "participant_id": "P7", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List PL1121", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P7_task_7", "participant_id": "P7", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1012", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P7_task_8", "participant_id": "P7", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00119, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P7_task_9", "participant_id": "P7", "complexity": "hard", "quality_condition": "Q2", "query_string": "For Order ORBOX00115, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P7_task_10", "participant_id": "P7", "complexity": "hard", "quality_condition": "Q3", "query_string": "For Order ORBOX00119, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q3_dataset"}], "P8": [{"task_id": "P8_task_1", "participant_id": "P8", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100085 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P8_task_2", "participant_id": "P8", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List PL1118", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P8_task_3", "participant_id": "P8", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1019", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P8_task_4", "participant_id": "P8", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1114", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P8_task_5", "participant_id": "P8", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100055 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P8_task_6", "participant_id": "P8", "complexity": "hard", "quality_condition": "Q3", "query_string": "For Order ORBOX0013, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P8_task_7", "participant_id": "P8", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100094 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P8_task_8", "participant_id": "P8", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100013 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P8_task_9", "participant_id": "P8", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00115, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P8_task_10", "participant_id": "P8", "complexity": "hard", "quality_condition": "Q1", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q1_dataset"}], "P9": [{"task_id": "P9_task_1", "participant_id": "P9", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00114, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P9_task_2", "participant_id": "P9", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0012, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P9_task_3", "participant_id": "P9", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00118, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P9_task_4", "participant_id": "P9", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100055 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P9_task_5", "participant_id": "P9", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100064 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P9_task_6", "participant_id": "P9", "complexity": "easy", "quality_condition": "Q3", "query_string": "Find all gears for Packing List PL1013", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P9_task_7", "participant_id": "P9", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1115", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P9_task_8", "participant_id": "P9", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0018, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P9_task_9", "participant_id": "P9", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100054 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P9_task_10", "participant_id": "P9", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List PL1019", "dataset_path": "data/experimental_datasets/Q2_dataset"}]}