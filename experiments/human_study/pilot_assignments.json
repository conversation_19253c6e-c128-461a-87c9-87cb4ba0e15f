{"P1": [{"task_id": "P1_task_1", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q2", "query_string": "For Order ORBOX00115, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P1_task_2", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q2", "query_string": "Determine the printer for Part 3DOR100026 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P1_task_3", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List ORBOX00121", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P1_task_4", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100058 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P1_task_5", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List ORBOX00114", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_6", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List ORBOX0014", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_7", "participant_id": "P1", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List ORBOX0017", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P1_task_8", "participant_id": "P1", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR10001 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_9", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX00121, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P1_task_10", "participant_id": "P1", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0017, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}], "P2": [{"task_id": "P2_task_1", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100031 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_2", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q0", "query_string": "Find all gears for Packing List ORBOX0018", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_3", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q0", "query_string": "Determine the printer for Part 3DOR100074 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_4", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q2", "query_string": "For Order ORBOX0019, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P2_task_5", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q1", "query_string": "Find all gears for Packing List ORBOX00121", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P2_task_6", "participant_id": "P2", "complexity": "easy", "quality_condition": "Q2", "query_string": "Find all gears for Packing List ORBOX0019", "dataset_path": "data/experimental_datasets/Q2_dataset"}, {"task_id": "P2_task_7", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q1", "query_string": "Determine the printer for Part 3DOR100098 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q1_dataset"}, {"task_id": "P2_task_8", "participant_id": "P2", "complexity": "medium", "quality_condition": "Q3", "query_string": "Determine the printer for Part 3DOR100082 and count parts printed on that machine", "dataset_path": "data/experimental_datasets/Q3_dataset"}, {"task_id": "P2_task_9", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0018, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}, {"task_id": "P2_task_10", "participant_id": "P2", "complexity": "hard", "quality_condition": "Q0", "query_string": "For Order ORBOX0014, verify ARC document date matches warehouse arrival", "dataset_path": "data/experimental_datasets/Q0_baseline"}]}