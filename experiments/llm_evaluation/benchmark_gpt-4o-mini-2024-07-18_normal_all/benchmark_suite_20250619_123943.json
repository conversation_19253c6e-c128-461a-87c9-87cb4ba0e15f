{"timestamp": "2025-06-19T12:39:43.818183", "models_evaluated": ["gpt-4o-mini-2024-07-18"], "total_tasks": 90, "benchmark_results": {"gpt-4o-mini-2024-07-18": {"model_name": "gpt-4o-mini-2024-07-18", "overall_score": 0.4179999999999999, "rank": 1, "complexity_scores": {"easy": 0.4666666666666667, "medium": 0.43333333333333335, "hard": 0.2}, "quality_scores": {"Q0": 0.2, "Q1": 0.26666666666666666, "Q2": 0.4, "Q3": 0.9333333333333333}, "metrics": {"accuracy": 0.36666666666666664, "avg_completion_time": 142.42955555555554, "avg_cost": 0.0, "avg_confidence": 0.14222222222222222, "error_detection_rate": 1.0, "token_efficiency": 0.03387728620352652}}}, "statistical_comparison": {}, "prompt_effectiveness": {"short": {"prompt_length": "short", "accuracy": 0.3333333333333333, "avg_time": 84.56600566953834, "avg_cost": 0.0, "avg_confidence": 0.12846782524776623, "token_usage": {"avg_input_tokens": 1214.6389490281495, "avg_output_tokens": 7767.022222222222, "total_tokens": 808349.5054125334}, "task_count": 90}, "normal": {"prompt_length": "normal", "accuracy": 0.36666666666666664, "avg_time": 120.78463127150935, "avg_cost": 0.0, "avg_confidence": 0.13429279591999774, "token_usage": {"avg_input_tokens": 2101.354757047159, "avg_output_tokens": 7767.022222222222, "total_tokens": 888153.9281342443}, "task_count": 90}, "long": {"prompt_length": "long", "accuracy": 0.36666666666666664, "avg_time": 142.42955555555554, "avg_cost": 0.0, "avg_confidence": 0.14222222222222222, "token_usage": {"avg_input_tokens": 3056.3555555555554, "avg_output_tokens": 7767.022222222222, "total_tokens": 974104.0}, "task_count": 90}}, "manufacturing_metrics": {"data_quality_handling": {"Q0": {"accuracy": 0.2, "avg_confidence": 0.21333333333333337, "task_count": 45, "error_detection_rate": 1.0}, "Q1": {"accuracy": 0.26666666666666666, "avg_confidence": 0.05999999999999999, "task_count": 15, "error_detection_rate": 1.0}, "Q2": {"accuracy": 0.4, "avg_confidence": 0.08, "task_count": 15, "error_detection_rate": 1.0}, "Q3": {"accuracy": 0.9333333333333333, "avg_confidence": 0.07333333333333333, "task_count": 15, "error_detection_rate": 1.0}}, "task_complexity_performance": {"easy": {"accuracy": 0.4666666666666667, "avg_time": 127.02233333333336, "avg_cost": 0.0, "task_count": 30}, "medium": {"accuracy": 0.43333333333333335, "avg_time": 152.81899999999996, "avg_cost": 0.0, "task_count": 30}, "hard": {"accuracy": 0.2, "avg_time": 147.44733333333335, "avg_cost": 0.0, "task_count": 30}}, "error_detection_capabilities": {}, "cross_system_validation": {"overall_integration_success": 0.36666666666666664, "data_reconciliation_quality": 0.14222222222222222, "system_complexity_handling": {"single_system_tasks": 0.4666666666666667, "multi_system_tasks": 0.31666666666666665}}, "manufacturing_domain_accuracy": {"gear_identification": {"accuracy": 0.4666666666666667, "avg_confidence": 0.4266666666666667, "task_count": 30}, "printer_analysis": {"accuracy": 0.43333333333333335, "avg_confidence": 0.0, "task_count": 30}, "compliance_verification": {"accuracy": 0.2, "avg_confidence": 0.0, "task_count": 30}}}, "recommendations": {"data_quality": "\n            Weakest Performance on: Q0 conditions (20.0% accuracy)\n            Recommendation: Implement additional data validation and error handling for Q0 scenarios\n            Consider prompt engineering improvements for data quality issue detection\n            ", "task_complexity": "\n            Most Challenging Tasks: hard (20.0% accuracy)\n            Recommendation: Consider task decomposition or specialized prompting for hard tasks\n            Average time for hard tasks: 147.4s\n            ", "manufacturing_domain": "\n            Weakest Manufacturing Domain: compliance_verification (20.0% accuracy)\n            Recommendation: Develop domain-specific training or fine-tuning for compliance_verification\n            Consider adding more context or examples for this task type\n            "}}