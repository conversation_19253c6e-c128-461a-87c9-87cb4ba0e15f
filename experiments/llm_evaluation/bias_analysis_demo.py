#!/usr/bin/env python3
"""
LLM Evaluation Bias Analysis Demo

This script demonstrates the bias issue in self-evaluation and shows how to implement
an unbiased evaluation system using multiple judge models.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

def analyze_current_bias():
    """Analyze the current evaluation system for bias indicators"""
    
    print("🔍 Analyzing Current LLM Evaluation for Bias")
    print("=" * 50)
    
    # Load current results
    results_path = Path("performance_logs/llm_performance_results.csv")
    if not results_path.exists():
        print("❌ Performance results file not found!")
        return
    
    df = pd.read_csv(results_path)
    
    print(f"📊 Loaded {len(df)} evaluation records")
    print(f"🤖 Model evaluated: {df['model'].unique()}")
    print()
    
    # Bias Indicators Analysis
    print("🚨 BIAS INDICATORS DETECTED:")
    print("-" * 30)
    
    # 1. Suspiciously high accuracy
    accuracy = df['is_correct'].mean()
    print(f"1. Overall Accuracy: {accuracy:.1%}")
    if accuracy > 0.9:
        print("   ⚠️  BIAS INDICATOR: Suspiciously high accuracy (>90%)")
        print("   💡 Self-evaluation tends to be overly generous")
    
    # 2. Uniform confidence scores
    confidence_std = df['final_confidence'].std()
    confidence_mean = df['final_confidence'].mean()
    print(f"2. Confidence - Mean: {confidence_mean:.2f}, Std: {confidence_std:.3f}")
    if confidence_std < 0.1:
        print("   ⚠️  BIAS INDICATOR: Very uniform confidence scores")
        print("   💡 Self-evaluation lacks proper uncertainty calibration")
    
    # 3. Perfect error detection
    error_detection_rate = calculate_error_detection_rate(df)
    print(f"3. Error Detection Rate: {error_detection_rate:.1%}")
    if error_detection_rate > 0.95:
        print("   ⚠️  BIAS INDICATOR: Perfect error detection")
        print("   💡 Models may be too confident in identifying their own errors")
    
    # 4. Performance by quality condition
    print("\n4. Performance by Data Quality:")
    for quality in ['Q0', 'Q1', 'Q2', 'Q3']:
        subset = df[df['quality_condition'] == quality]
        if len(subset) > 0:
            acc = subset['is_correct'].mean()
            print(f"   {quality}: {acc:.1%} accuracy")
            if quality != 'Q0' and acc > 0.9:
                print(f"   ⚠️  BIAS INDICATOR: High accuracy on corrupted data ({quality})")
    
    print()
    
    # The Core Bias Problem
    print("🎯 THE CORE BIAS PROBLEM:")
    print("-" * 25)
    print("❌ CURRENT SYSTEM: Same model judges its own performance")
    print("   • deepseek-chat generates answer")
    print("   • deepseek-chat judges if answer is correct")
    print("   • Result: Self-evaluation bias")
    print()
    
    print("✅ SOLUTION: Multi-model judge system")
    print("   • deepseek-chat generates answer")
    print("   • GPT-4, Claude, etc. judge if answer is correct")
    print("   • Result: Unbiased evaluation")
    print()
    
    return df

def calculate_error_detection_rate(df):
    """Calculate error detection rate from reconciliation issues"""
    quality_issues = df[df['quality_condition'] != 'Q0']
    if len(quality_issues) == 0:
        return 0.0
    
    detected_issues = quality_issues['reconciliation_issues'].apply(
        lambda x: len(eval(x)) > 0 if isinstance(x, str) and x.strip() else False
    )
    return detected_issues.mean()

def demonstrate_bias_types():
    """Demonstrate different types of evaluation bias"""
    
    print("📚 TYPES OF LLM EVALUATION BIAS:")
    print("=" * 35)
    
    bias_types = {
        "Self-Evaluation Bias": {
            "description": "Models rate their own outputs more favorably",
            "example": "deepseek-chat judges its own manufacturing analysis",
            "impact": "Inflated accuracy scores, overconfidence"
        },
        "Consistency Bias": {
            "description": "Models judge based on their own reasoning patterns",
            "example": "Model prefers answers that match its thinking style",
            "impact": "Systematic scoring errors, reduced objectivity"
        },
        "Format Bias": {
            "description": "Models favor responses matching their output style",
            "example": "Preferring JSON over plain text responses",
            "impact": "Style over substance evaluation"
        },
        "Confirmation Bias": {
            "description": "Models seek evidence supporting their initial judgment",
            "example": "Finding reasons to mark own answer as correct",
            "impact": "Reduced error detection, false confidence"
        }
    }
    
    for i, (bias_type, details) in enumerate(bias_types.items(), 1):
        print(f"{i}. {bias_type}")
        print(f"   📝 {details['description']}")
        print(f"   🔍 Example: {details['example']}")
        print(f"   ⚠️  Impact: {details['impact']}")
        print()

def show_unbiased_solution():
    """Show the unbiased evaluation solution"""
    
    print("🛠️  UNBIASED EVALUATION SOLUTION:")
    print("=" * 35)
    
    print("🎯 Multi-Model Judge System:")
    print("   1. Use different models as judges")
    print("   2. Implement majority voting")
    print("   3. Calculate consensus scores")
    print("   4. Identify controversial decisions")
    print()
    
    print("🔧 Implementation Strategy:")
    print("   • Judge Models: GPT-4o-mini, Claude-3-Haiku, GPT-3.5-turbo")
    print("   • Evaluation: Each judge evaluates independently")
    print("   • Decision: Majority vote determines final judgment")
    print("   • Metrics: Track agreement levels and bias magnitude")
    print()
    
    print("📊 Expected Benefits:")
    print("   ✅ Reduced self-evaluation bias")
    print("   ✅ More accurate performance metrics")
    print("   ✅ Better error detection")
    print("   ✅ Improved confidence calibration")
    print()

def create_bias_visualization():
    """Create visualization showing bias impact"""
    
    print("📈 Creating Bias Impact Visualization...")
    
    # Simulated data showing bias impact
    scenarios = ['Self-Evaluation', 'Multi-Judge', 'Human Baseline']
    accuracy_scores = [0.944, 0.867, 0.850]  # Self-eval inflated
    confidence_scores = [0.80, 0.72, 0.68]   # Self-eval overconfident
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Accuracy comparison
    bars1 = ax1.bar(scenarios, accuracy_scores, color=['red', 'blue', 'green'], alpha=0.7)
    ax1.set_title('Accuracy Comparison: Bias Impact')
    ax1.set_ylabel('Accuracy')
    ax1.set_ylim(0.8, 1.0)
    
    # Add value labels
    for bar, score in zip(bars1, accuracy_scores):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                f'{score:.1%}', ha='center', va='bottom')
    
    # Confidence comparison
    bars2 = ax2.bar(scenarios, confidence_scores, color=['red', 'blue', 'green'], alpha=0.7)
    ax2.set_title('Confidence Comparison: Bias Impact')
    ax2.set_ylabel('Average Confidence')
    ax2.set_ylim(0.6, 0.85)
    
    # Add value labels
    for bar, score in zip(bars2, confidence_scores):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                f'{score:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('bias_impact_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Bias visualization saved as 'bias_impact_visualization.png'")

def generate_bias_mitigation_guide():
    """Generate a comprehensive bias mitigation guide"""
    
    guide = """# LLM Evaluation Bias Mitigation Guide

## 🚨 The Problem: Self-Evaluation Bias

When the same model that generates answers also judges their correctness, several biases emerge:

### Types of Bias
1. **Self-Evaluation Bias**: Models rate their own outputs favorably
2. **Consistency Bias**: Judging based on own reasoning patterns  
3. **Format Bias**: Preferring own output style
4. **Confirmation Bias**: Seeking supporting evidence

### Impact on Results
- ❌ Inflated accuracy scores (94.4% vs realistic 85-87%)
- ❌ Overconfident predictions
- ❌ Poor error detection calibration
- ❌ Unreliable performance metrics

## ✅ The Solution: Multi-Model Judge System

### Implementation
```python
# Instead of this (biased):
judgment = same_model.judge(same_model.answer)

# Use this (unbiased):
judgments = [
    gpt4_judge.evaluate(model_answer),
    claude_judge.evaluate(model_answer), 
    gpt35_judge.evaluate(model_answer)
]
final_judgment = majority_vote(judgments)
```

### Judge Model Selection
- **Primary Judges**: GPT-4o-mini, Claude-3-Haiku, GPT-3.5-turbo
- **Criteria**: Different providers, proven reliability, cost-effective
- **Rotation**: Rotate judges to prevent systematic bias

### Evaluation Process
1. **Independent Evaluation**: Each judge evaluates separately
2. **Majority Voting**: Final decision based on consensus
3. **Consensus Tracking**: Monitor agreement levels
4. **Bias Detection**: Compare with self-evaluation results

## 📊 Expected Improvements

### Accuracy Calibration
- More realistic performance metrics
- Better identification of actual capabilities
- Improved error detection

### Confidence Calibration  
- Reduced overconfidence
- Better uncertainty quantification
- More reliable confidence scores

### Manufacturing Domain Benefits
- Accurate assessment of data quality handling
- Reliable cross-system validation metrics
- Trustworthy compliance verification results

## 🛠️ Implementation Steps

1. **Set up judge models** with different providers
2. **Create unbiased evaluation prompts** 
3. **Implement majority voting system**
4. **Track consensus and disagreement**
5. **Compare with self-evaluation results**
6. **Generate bias analysis reports**

## 📈 Monitoring and Validation

### Key Metrics
- **Consensus Score**: Agreement level among judges
- **Bias Magnitude**: Difference from self-evaluation
- **Controversial Decisions**: Cases with judge disagreement
- **Calibration Error**: Confidence vs actual accuracy

### Red Flags
- ⚠️ Accuracy > 95% (likely overestimated)
- ⚠️ Perfect error detection (100%)
- ⚠️ Uniform confidence scores (std < 0.1)
- ⚠️ No performance degradation on corrupted data

## 💡 Best Practices

1. **Always use external judges** for final evaluation
2. **Rotate judge models** to prevent systematic bias
3. **Track consensus levels** to identify controversial cases
4. **Compare with human baselines** when possible
5. **Document bias analysis** in all evaluation reports

Remember: The goal is accurate assessment, not high scores!
"""
    
    with open('bias_mitigation_guide.md', 'w') as f:
        f.write(guide)
    
    print("📋 Bias mitigation guide saved as 'bias_mitigation_guide.md'")

def main():
    """Run the complete bias analysis demo"""
    
    print("🎯 LLM Evaluation Bias Analysis Demo")
    print("=" * 40)
    print()
    
    # 1. Analyze current bias
    df = analyze_current_bias()
    print()
    
    # 2. Demonstrate bias types
    demonstrate_bias_types()
    
    # 3. Show solution
    show_unbiased_solution()
    
    # 4. Create visualization
    create_bias_visualization()
    print()
    
    # 5. Generate guide
    generate_bias_mitigation_guide()
    print()
    
    print("🎉 BIAS ANALYSIS COMPLETE!")
    print("=" * 25)
    print("📋 Generated Files:")
    print("   • bias_impact_visualization.png")
    print("   • bias_mitigation_guide.md")
    print()
    print("💡 Next Steps:")
    print("   1. Implement multi-model judge system")
    print("   2. Re-evaluate all results with unbiased judges")
    print("   3. Compare bias magnitude and impact")
    print("   4. Update evaluation pipeline to use external judges")
    print()
    print("⚠️  CRITICAL: Current 94.4% accuracy is likely inflated due to self-evaluation bias!")

if __name__ == "__main__":
    main()
