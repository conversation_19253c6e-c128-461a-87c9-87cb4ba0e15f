{"timestamp": "2025-06-17T09:58:03.278224", "models_evaluated": ["deepseek-chat"], "total_tasks": 90, "benchmark_results": {"deepseek-chat": {"model_name": "deepseek-chat", "overall_score": 0.6099929629629629, "rank": 1, "complexity_scores": {"easy": 0.8666666666666667, "medium": 0.13333333333333333, "hard": 0.9666666666666667}, "quality_scores": {"Q0": 0.7111111111111111, "Q1": 0.6666666666666666, "Q2": 0.4, "Q3": 0.7333333333333333}, "metrics": {"accuracy": 0.6555555555555556, "avg_completion_time": 44.918777777777784, "avg_cost": 0.0, "avg_confidence": 0.3166666666666666, "error_detection_rate": 1.0, "token_efficiency": 0.21199001131811077}}}, "statistical_comparison": {}, "prompt_effectiveness": {"short": {"prompt_length": "short", "accuracy": 0.5888888888888889, "avg_time": 26.660709149439104, "avg_cost": 0.0, "avg_confidence": 0.2833539608331005, "token_usage": {"avg_input_tokens": 1065.7680536874695, "avg_output_tokens": 405.6222222222222, "total_tokens": 132425.12483187226}, "task_count": 90}, "normal": {"prompt_length": "normal", "accuracy": 0.6555555555555556, "avg_time": 38.450060757328764, "avg_cost": 0.0, "avg_confidence": 0.3003230528352574, "token_usage": {"avg_input_tokens": 1869.2586729741051, "avg_output_tokens": 405.6222222222222, "total_tokens": 204739.28056766946}, "task_count": 90}, "long": {"prompt_length": "long", "accuracy": 0.6555555555555556, "avg_time": 44.918777777777784, "avg_cost": 0.0, "avg_confidence": 0.3166666666666666, "token_usage": {"avg_input_tokens": 2686.766666666667, "avg_output_tokens": 405.6222222222222, "total_tokens": 278315.0}, "task_count": 90}}, "manufacturing_metrics": {"data_quality_handling": {"Q0": {"accuracy": 0.7111111111111111, "avg_confidence": 0.3333333333333334, "task_count": 45, "error_detection_rate": 1.0}, "Q1": {"accuracy": 0.6666666666666666, "avg_confidence": 0.29999999999999993, "task_count": 15, "error_detection_rate": 1.0}, "Q2": {"accuracy": 0.4, "avg_confidence": 0.29999999999999993, "task_count": 15, "error_detection_rate": 1.0}, "Q3": {"accuracy": 0.7333333333333333, "avg_confidence": 0.29999999999999993, "task_count": 15, "error_detection_rate": 1.0}}, "task_complexity_performance": {"easy": {"accuracy": 0.8666666666666667, "avg_time": 44.93733333333333, "avg_cost": 0.0, "task_count": 30}, "medium": {"accuracy": 0.13333333333333333, "avg_time": 46.22533333333333, "avg_cost": 0.0, "task_count": 30}, "hard": {"accuracy": 0.9666666666666667, "avg_time": 43.593666666666664, "avg_cost": 0.0, "task_count": 30}}, "error_detection_capabilities": {}, "cross_system_validation": {"overall_integration_success": 0.6555555555555556, "data_reconciliation_quality": 0.3166666666666666, "system_complexity_handling": {"single_system_tasks": 0.8666666666666667, "multi_system_tasks": 0.55}}, "manufacturing_domain_accuracy": {"gear_identification": {"accuracy": 0.8666666666666667, "avg_confidence": 0.3166666666666667, "task_count": 30}, "printer_analysis": {"accuracy": 0.13333333333333333, "avg_confidence": 0.3333333333333334, "task_count": 30}, "compliance_verification": {"accuracy": 0.9666666666666667, "avg_confidence": 0.30000000000000004, "task_count": 30}}}, "recommendations": {"data_quality": "\n            Weakest Performance on: Q2 conditions (40.0% accuracy)\n            Recommendation: Implement additional data validation and error handling for Q2 scenarios\n            Consider prompt engineering improvements for data quality issue detection\n            ", "task_complexity": "\n            Most Challenging Tasks: medium (13.3% accuracy)\n            Recommendation: Consider task decomposition or specialized prompting for medium tasks\n            Average time for medium tasks: 46.2s\n            ", "manufacturing_domain": "\n            Weakest Manufacturing Domain: printer_analysis (13.3% accuracy)\n            Recommendation: Develop domain-specific training or fine-tuning for printer_analysis\n            Consider adding more context or examples for this task type\n            "}}