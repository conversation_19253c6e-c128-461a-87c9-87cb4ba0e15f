{"timestamp": "2025-06-18T09:55:20.151363", "models_evaluated": ["deepseek-reasoner"], "total_tasks": 90, "benchmark_results": {"deepseek-reasoner": {"model_name": "deepseek-reasoner", "overall_score": 0.6403888888888888, "rank": 1, "complexity_scores": {"easy": 0.7333333333333333, "medium": 0.5333333333333333, "hard": 1.0}, "quality_scores": {"Q0": 0.8444444444444444, "Q1": 0.8, "Q2": 0.6, "Q3": 0.6}, "metrics": {"accuracy": 0.7555555555555555, "avg_completion_time": 200.83822222222224, "avg_cost": 0.0, "avg_confidence": 0.5877777777777777, "error_detection_rate": 1.0, "token_efficiency": 0.08967072382735748}}}, "statistical_comparison": {}, "prompt_effectiveness": {"short": {"prompt_length": "short", "accuracy": 0.7, "avg_time": 122.418618009852, "avg_cost": 0.0, "avg_confidence": 0.5247293606300998, "token_usage": {"avg_input_tokens": 1140.4382479140152, "avg_output_tokens": 5589.277777777777, "total_tokens": 605674.4423122613}, "task_count": 90}, "normal": {"prompt_length": "normal", "accuracy": 0.7555555555555555, "avg_time": 168.49841540427317, "avg_cost": 0.0, "avg_confidence": 0.5578515755117058, "token_usage": {"avg_input_tokens": 1965.1016157125007, "avg_output_tokens": 5589.277777777777, "total_tokens": 679894.145414125}, "task_count": 90}, "long": {"prompt_length": "long", "accuracy": 0.7555555555555555, "avg_time": 200.83822222222224, "avg_cost": 0.0, "avg_confidence": 0.5877777777777777, "token_usage": {"avg_input_tokens": 2836.6111111111113, "avg_output_tokens": 5589.277777777777, "total_tokens": 758330.0}, "task_count": 90}}, "manufacturing_metrics": {"data_quality_handling": {"Q0": {"accuracy": 0.8444444444444444, "avg_confidence": 0.6133333333333334, "task_count": 45, "error_detection_rate": 1.0}, "Q1": {"accuracy": 0.8, "avg_confidence": 0.56, "task_count": 15, "error_detection_rate": 1.0}, "Q2": {"accuracy": 0.6, "avg_confidence": 0.5733333333333334, "task_count": 15, "error_detection_rate": 1.0}, "Q3": {"accuracy": 0.6, "avg_confidence": 0.5533333333333333, "task_count": 15, "error_detection_rate": 1.0}}, "task_complexity_performance": {"easy": {"accuracy": 0.7333333333333333, "avg_time": 176.90466666666666, "avg_cost": 0.0, "task_count": 30}, "medium": {"accuracy": 0.5333333333333333, "avg_time": 274.336, "avg_cost": 0.0, "task_count": 30}, "hard": {"accuracy": 1.0, "avg_time": 151.27399999999997, "avg_cost": 0.0, "task_count": 30}}, "error_detection_capabilities": {}, "cross_system_validation": {"overall_integration_success": 0.7555555555555555, "data_reconciliation_quality": 0.5877777777777777, "system_complexity_handling": {"single_system_tasks": 0.7333333333333333, "multi_system_tasks": 0.7666666666666667}}, "manufacturing_domain_accuracy": {"gear_identification": {"accuracy": 0.7333333333333333, "avg_confidence": 0.8000000000000003, "task_count": 30}, "printer_analysis": {"accuracy": 0.5333333333333333, "avg_confidence": 0.1633333333333333, "task_count": 30}, "compliance_verification": {"accuracy": 1.0, "avg_confidence": 0.8000000000000003, "task_count": 30}}}, "recommendations": {"data_quality": "\n            Weakest Performance on: Q2 conditions (60.0% accuracy)\n            Recommendation: Implement additional data validation and error handling for Q2 scenarios\n            Consider prompt engineering improvements for data quality issue detection\n            ", "task_complexity": "\n            Most Challenging Tasks: medium (53.3% accuracy)\n            Recommendation: Consider task decomposition or specialized prompting for medium tasks\n            Average time for medium tasks: 274.3s\n            ", "manufacturing_domain": "\n            Weakest Manufacturing Domain: printer_analysis (53.3% accuracy)\n            Recommendation: Develop domain-specific training or fine-tuning for printer_analysis\n            Consider adding more context or examples for this task type\n            "}}