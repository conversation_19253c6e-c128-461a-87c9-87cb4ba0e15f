{"timestamp": "2025-06-17T10:21:46.941207", "models_evaluated": ["deepseek-chat"], "total_tasks": 90, "benchmark_results": {"deepseek-chat": {"model_name": "deepseek-chat", "overall_score": 0.644602962962963, "rank": 1, "complexity_scores": {"easy": 0.8333333333333334, "medium": 0.7666666666666667, "hard": 0.43333333333333335}, "quality_scores": {"Q0": 0.5555555555555556, "Q1": 0.8, "Q2": 0.6666666666666666, "Q3": 0.9333333333333333}, "metrics": {"accuracy": 0.6777777777777778, "avg_completion_time": 35.602444444444444, "avg_cost": 0.0, "avg_confidence": 0.28111111111111114, "error_detection_rate": 1.0, "token_efficiency": 0.16442757639143465}}}, "statistical_comparison": {}, "prompt_effectiveness": {"short": {"prompt_length": "short", "accuracy": 0.6222222222222222, "avg_time": 21.183878092421374, "avg_cost": 0.0, "avg_confidence": 0.25279869788091414, "token_usage": {"avg_input_tokens": 1434.8060327783653, "avg_output_tokens": 569.9111111111112, "total_tokens": 180424.54295005288}, "task_count": 90}, "normal": {"prompt_length": "normal", "accuracy": 0.6777777777777778, "avg_time": 30.51820115149399, "avg_cost": 0.0, "avg_confidence": 0.26739568193546803, "token_usage": {"avg_input_tokens": 2489.441265824171, "avg_output_tokens": 569.9111111111112, "total_tokens": 275341.71392417536}, "task_count": 90}, "long": {"prompt_length": "long", "accuracy": 0.6777777777777778, "avg_time": 35.602444444444444, "avg_cost": 0.0, "avg_confidence": 0.28111111111111114, "token_usage": {"avg_input_tokens": 3552.133333333333, "avg_output_tokens": 569.9111111111112, "total_tokens": 370984.0}, "task_count": 90}}, "manufacturing_metrics": {"data_quality_handling": {"Q0": {"accuracy": 0.5555555555555556, "avg_confidence": 0.2688888888888889, "task_count": 45, "error_detection_rate": 1.0}, "Q1": {"accuracy": 0.8, "avg_confidence": 0.2, "task_count": 15, "error_detection_rate": 1.0}, "Q2": {"accuracy": 0.6666666666666666, "avg_confidence": 0.33999999999999997, "task_count": 15, "error_detection_rate": 1.0}, "Q3": {"accuracy": 0.9333333333333333, "avg_confidence": 0.33999999999999997, "task_count": 15, "error_detection_rate": 1.0}}, "task_complexity_performance": {"easy": {"accuracy": 0.8333333333333334, "avg_time": 43.81033333333333, "avg_cost": 0.0, "task_count": 30}, "medium": {"accuracy": 0.7666666666666667, "avg_time": 27.071333333333328, "avg_cost": 0.0, "task_count": 30}, "hard": {"accuracy": 0.43333333333333335, "avg_time": 35.925666666666665, "avg_cost": 0.0, "task_count": 30}}, "error_detection_capabilities": {}, "cross_system_validation": {"overall_integration_success": 0.6777777777777778, "data_reconciliation_quality": 0.28111111111111114, "system_complexity_handling": {"single_system_tasks": 0.8333333333333334, "multi_system_tasks": 0.6}}, "manufacturing_domain_accuracy": {"gear_identification": {"accuracy": 0.8333333333333334, "avg_confidence": 0.7400000000000002, "task_count": 30}, "printer_analysis": {"accuracy": 0.7666666666666667, "avg_confidence": 0.10333333333333333, "task_count": 30}, "compliance_verification": {"accuracy": 0.43333333333333335, "avg_confidence": 0.0, "task_count": 30}}}, "recommendations": {"data_quality": "\n            Weakest Performance on: Q0 conditions (55.6% accuracy)\n            Recommendation: Implement additional data validation and error handling for Q0 scenarios\n            Consider prompt engineering improvements for data quality issue detection\n            ", "task_complexity": "\n            Most Challenging Tasks: hard (43.3% accuracy)\n            Recommendation: Consider task decomposition or specialized prompting for hard tasks\n            Average time for hard tasks: 35.9s\n            ", "manufacturing_domain": "\n            Weakest Manufacturing Domain: compliance_verification (43.3% accuracy)\n            Recommendation: Develop domain-specific training or fine-tuning for compliance_verification\n            Consider adding more context or examples for this task type\n            "}}