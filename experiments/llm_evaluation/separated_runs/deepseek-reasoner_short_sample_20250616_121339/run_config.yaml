cost_analysis:
  human_analyst_wage: 25.0
  roi_calculation: true
  target_cost_per_task: 0.5
data_quality:
  conditions:
    Q0:
      corruption_rate: 0.0
      description: Perfect data with full traceability
      name: baseline
    Q1:
      corruption_rate: 0.15
      error_type: random_space_injection
      name: spaces
      spaces_per_corruption:
      - 1
      - 2
      - 3
      target_fields:
      - barcode_ids
      - worker_rfid
      - order_numbers
    Q2:
      characters_per_corruption: 1
      corruption_rate: 0.12
      error_type: random_character_removal
      name: char_missing
      target_fields:
      - gear_ids
      - order_ids
    Q3:
      corruption_rate: 0.07
      maintain_solvability: true
      name: missing_records
      removal_strategy: strategic_intermediate_links
      target_systems:
      - relationship_data
      - location_data
experiment:
  name: Manufacturing_Data_Assistant_Study
  random_seed: 42
  version: '1.0'
ground_truth:
  answer_formats:
    easy: list_of_gear_ids
    hard: date_match_boolean_with_discrepancy_details
    medium: printer_id_and_count
  generation_method: baseline_data_traversal
  validation_required: true
human_study:
  data_collection:
    method: manual_researcher_recording
    response_format: structured_forms
    timing_precision: seconds
  participant_matrix:
    P1:
      prompt_pattern: PC1
      quality_pattern: PQ1
    P2:
      prompt_pattern: PC2
      quality_pattern: PQ1
    P3:
      prompt_pattern: PC3
      quality_pattern: PQ1
    P4:
      prompt_pattern: PC1
      quality_pattern: PQ2
    P5:
      prompt_pattern: PC2
      quality_pattern: PQ2
    P6:
      prompt_pattern: PC3
      quality_pattern: PQ2
    P7:
      prompt_pattern: PC1
      quality_pattern: PQ3
    P8:
      prompt_pattern: PC2
      quality_pattern: PQ3
    P9:
      prompt_pattern: PC3
      quality_pattern: PQ3
  participants: 9
  prompt_patterns:
    PC1:
      E: 4
      H: 3
      M: 3
    PC2:
      E: 3
      H: 3
      M: 4
    PC3:
      E: 3
      H: 4
      M: 3
  quality_patterns:
    PQ1:
      Q0: 5
      Q1: 2
      Q2: 2
      Q3: 1
    PQ2:
      Q0: 5
      Q1: 2
      Q2: 1
      Q3: 2
    PQ3:
      Q0: 5
      Q1: 1
      Q2: 2
      Q3: 2
  success_criteria:
    error_detection_required: true
    partial_credit: false
    scoring_method: binary
  target_counts:
    by_complexity:
      E: 30
      H: 30
      M: 30
    by_quality:
      Q0: 45
      Q1: 15
      Q2: 15
      Q3: 15
    total_tasks: 90
  tasks_per_participant: 10
llm_evaluation:
  metrics:
    manufacturing_specific:
    - data_traversal_correctness
    - cross_system_validation
    primary:
    - accuracy
    - completion_time
    - api_cost
    secondary:
    - error_detection
    - confidence_score
    - token_usage
  models_to_test:
  - deepseek-reasoner
  prompt_length: short
  task_distribution: identical_to_human_study
  task_subset: sample
  tasks_per_model: 90
llm_providers:
  anthropic:
    api_key_env: ANTHROPIC_API_KEY
    base_url: https://api.anthropic.com/v1/
    models:
      claude-3-5-haiku-latest:
        cost_per_1m_tokens_input: 3
        cost_per_1m_tokens_output: 15
        max_tokens: 200000
        recommended: true
        temperature: 0.1
      claude-sonnet-4-20250514:
        cost_per_1m_tokens_input: 3
        cost_per_1m_tokens_output: 15
        max_tokens: 200000
        recommended: true
        temperature: 0.1
  deepseek:
    api_key_env: DEEPSEEK_API_KEY
    base_url: https://api.deepseek.com
    models:
      deepseek-chat:
        cost_per_1m_tokens_input: 0.07
        cost_per_1m_tokens_output: 1.1
        max_tokens: 200000
        recommended: true
        temperature: 0.1
      deepseek-reasoner:
        cost_per_1m_tokens_input: 0.55
        cost_per_1m_tokens_output: 2.19
        max_tokens: 200000
        recommended: true
        temperature: 0.1
  openai:
    api_key_env: OPENAI_API_KEY
    base_url: https://api.openai.com/v1/
    models:
      o3-mini-2025-01-31:
        cost_per_1m_tokens_input: 1.1
        cost_per_1m_tokens_output: 4.4
        max_tokens: 200000
        recommended: true
        temperature: 0.1
      o4-mini-2025-04-16:
        cost_per_1m_tokens_input: 1.1
        cost_per_1m_tokens_output: 4.4
        max_tokens: 200000
        recommended: true
        temperature: 0.1
master_agent:
  confidence_threshold: 0.7
  error_handling:
    max_alternative_attempts: 2
    tier_1: standard_execution
    tier_2: alternative_reasoning
    tier_3: data_quality_flagging
  max_execution_attempts: 3
  max_planning_steps: 5
  name: ManufacturingDataMaster
  planning:
    decomposition_method: task_complexity_based
    fallback_enabled: true
    tool_selection_strategy: manufacturing_priority
  role: Manufacturing Data Analysis Orchestrator
master_agent_planning_prompt: "You are a JSON formatting expert. Your ONLY function\
  \ is to convert a user's query into a JSON object representing an execution plan.\n\
  DO NOT add any introductory text, explanations, or markdown formatting. Your response\
  \ MUST be ONLY the raw JSON object.\n\nThe JSON object must have two keys: \"complexity\"\
  \ and \"plan\".\n- \"complexity\": A string, one of \"easy\", \"medium\", or \"\
  hard\".\n- \"plan\": A list of step objects.\n\nEach step object in the \"plan\"\
  \ list must contain:\n- \"step\": An integer (e.g., 1, 2, 3).\n- \"tool\": The exact\
  \ name of a tool from this list: {tool_descriptions}\n- \"input\": The input for\
  \ the tool. For step 1, use the ID from the user query. For later steps, reference\
  \ a previous output key (e.g., \"{{step_1_output['order_id']}}\").\n- \"output_key\"\
  : A unique string to identify this step's output (e.g., \"step_1_output\").\n- \"\
  reason\": A brief string explaining the step's purpose.\n\n---\nHere are three examples\
  \ of correct output format.\n\nExample 1 Query: \"Find all gears for Packing List\
  \ PL1011\"\nExample 1 Output:\n```json\n{{\n  \"complexity\": \"easy\",\n  \"plan\"\
  : [\n    {{\n      \"step\": 1,\n      \"tool\": \"packing_list_parser_tool\",\n\
  \      \"input\": \"PL1011\",\n      \"output_key\": \"step_1_order_id\",\n    \
  \  \"reason\": \"Parse the packing list to find the corresponding Order ID.\"\n\
  \    }},\n    {{\n      \"step\": 2,\n      \"tool\": \"relationship_tool\",\n \
  \     \"input\": \"{{step_1_order_id['order_id']}}\",\n      \"output_key\": \"\
  step_2_gear_list\",\n      \"reason\": \"Use the Order ID to find all related gear\
  \ parts.\"\n    }}\n  ]\n}}\n```\n\nExample 2 Query: \"Determine the printer for\
  \ Part 3DOR100091 and count parts printed on that machine\"\nExample 2 Output:\n\
  ```json\n{{\n  \"complexity\": \"medium\",\n  \"plan\": [\n    {{\n      \"step\"\
  : 1,\n      \"tool\": \"relationship_tool\",\n      \"input\": \"3DOR100091\",\n\
  \      \"output_key\": \"step_1_printer_info\",\n      \"reason\": \"Find the parent\
  \ printer associated with the given part ID.\"\n    }},\n    {{\n      \"step\"\
  : 2,\n      \"tool\": \"relationship_tool\",\n      \"input\": \"{{step_1_printer_info['parent']}}\"\
  ,\n      \"output_key\": \"step_2_all_parts_on_printer\",\n      \"reason\": \"\
  Use the found printer ID to query for all other parts associated with it to get\
  \ a total count.\"\n    }}\n  ]\n}}\n```\n\nExample 3 Query: \"For Order ORBOX0017,\
  \ verify ARC document date matches warehouse arrival\"\nExample 3 Output:\n```json\n\
  {{\n  \"complexity\": \"hard\",\n  \"plan\": [\n    {{\n      \"step\": 1,\n   \
  \   \"tool\": \"document_parser_tool\",\n      \"input\": \"ORBOX0017\",\n     \
  \ \"output_key\": \"step_1_arc_date\",\n      \"reason\": \"Parse the ARC document\
  \ to get the certificate completion date.\"\n    }},\n    {{\n      \"step\": 2,\n\
  \      \"tool\": \"location_query_tool\",\n      \"input\": \"ORBOX0017\",\n   \
  \   \"output_key\": \"step_2_warehouse_arrival\",\n      \"reason\": \"Find the\
  \ arrival date of the order at the Parts Warehouse.\"\n    }}\n  ]\n}}\n```\n---\n\
  \nNow, generate the JSON plan for the following user query. Remember, ONLY output\
  \ the JSON object."
model_selection:
  fallback_order:
  - o3-mini-2025-01-31
  - claude-3-5-haiku-latest
  - deepseek-chat
  primary_models:
  - o4-mini-2025-04-16
  - claude-sonnet-4-20250514
  - deepseek-reasoner
  selection_criteria: intelligence_price_ratio
monitoring:
  log_alternative_attempts: true
  track_confidence_scores: true
  track_execution_time: true
  track_token_usage: true
  track_tool_success_rates: true
output:
  generate_visualizations: true
  logging_level: INFO
  results_directory: experiments/results
  save_intermediate_results: true
performance_thresholds:
  max_cost_per_task: 8.0
  max_response_time: 120
rate_limits:
  anthropic:
    requests_per_minute: 400
    tokens_per_minute: 100000
  deepseek:
    requests_per_minute: 600
    tokens_per_minute: 300000
  openai:
    requests_per_minute: 500
    tokens_per_minute: 200000
retry_config:
  backoff_factor: 2
  max_retries: 3
  timeout_seconds: 60
specialist_agents:
  data_retrieval_agent:
    force_fuzzy_on_failure: true
    fuzzy_search_enabled: true
    name: DataRetrievalSpecialist
    role: Multi-source Manufacturing Data Query
    search_threshold: 0.75
    tools:
    - location_query
    - machine_log
    - relationship
    - worker_data
  reconciliation_agent:
    alternative_path_discovery: true
    missing_data_handling: true
    name: DataReconciliationSpecialist
    role: Cross-system Validation and Consistency
    tools:
    - relationship
    - barcode_validator
    - document_parser
  synthesis_agent:
    include_confidence: true
    include_data_quality_assessment: true
    name: ReportSynthesisSpecialist
    output_format: structured_manufacturing_report
    role: Manufacturing Report Generation
task_complexity:
  easy:
    data_sources:
    - documents
    - relationship_data
    description: Find all gears for Packing List {ENTITY_ID}
    expected_time_human: 180
    expected_time_llm: 30
    max_steps: 2
    name: Document Lookup and Traversal
  hard:
    data_sources:
    - documents
    - location_data
    - relationship_data
    description: For Order {ENTITY_ID}, verify ARC document date matches warehouse
      arrival
    expected_time_human: 900
    expected_time_llm: 120
    max_steps: 6
    name: Document Cross-Reference
  medium:
    data_sources:
    - relationship_data
    - machine_log
    - location_data
    description: Determine the printer for Part {ENTITY_ID} and count parts printed
      on that machine
    expected_time_human: 420
    expected_time_llm: 60
    max_steps: 4
    name: Cross-System Validation
tools:
  barcode_validator_tool:
    format_patterns:
      gear: 3DOR\d{5,6}
      material: '[A-Z]{3,4}\d{4}'
      order: OR[A-Z0-9]+
      printer: Printer_\d+
      worker_rfid: \d{10}
  document_parser_tool:
    certificate_types:
    - faa_8130_3
    date_extraction: true
    supported_formats:
    - pdf
  location_query_tool:
    fuzzy_matching: true
    max_location_results: 50
    search_algorithms:
    - exact
    - levenshtein
    - soundex
  machine_log_tool:
    duration_validation: true
    printer_correlation: true
    time_range_extension: true
  relationship_tool:
    alternative_path_limit: 3
    bidirectional_search: true
    max_depth: 5
  worker_data_tool:
    activity_correlation: true
    rfid_validation: true
    temporal_matching: true
workflow:
  alternative_flow:
  - alternative_tool_selection
  - sequential_execution
  - partial_result_compilation
  - confidence_assessment
  failure_handling:
  - error_classification
  - data_quality_assessment
  - partial_report_generation
  standard_flow:
  - query_decomposition
  - tool_selection
  - parallel_execution
  - result_validation
  - synthesis
