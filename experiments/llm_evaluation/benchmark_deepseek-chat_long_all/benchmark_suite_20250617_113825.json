{"timestamp": "2025-06-17T11:38:25.713950", "models_evaluated": ["deepseek-chat"], "total_tasks": 90, "benchmark_results": {"deepseek-chat": {"model_name": "deepseek-chat", "overall_score": 0.8278188888888889, "rank": 1, "complexity_scores": {"easy": 0.7666666666666667, "medium": 1.0, "hard": 1.0}, "quality_scores": {"Q0": 1.0, "Q1": 1.0, "Q2": 0.6666666666666666, "Q3": 0.8666666666666667}, "metrics": {"accuracy": 0.9222222222222223, "avg_completion_time": 23.721, "avg_cost": 0.0, "avg_confidence": 0.5866666666666667, "error_detection_rate": 1.0, "token_efficiency": 0.2867715398249657}}}, "statistical_comparison": {}, "prompt_effectiveness": {"short": {"prompt_length": "short", "accuracy": 0.8555555555555555, "avg_time": 14.28897283783676, "avg_cost": 0.0, "avg_confidence": 0.5277217050279103, "token_usage": {"avg_input_tokens": 1114.3375249234118, "avg_output_tokens": 427.8888888888889, "total_tokens": 138800.37724310707}, "task_count": 90}, "normal": {"prompt_length": "normal", "accuracy": 0.9222222222222223, "avg_time": 20.426770148975024, "avg_cost": 0.0, "avg_confidence": 0.5588421955017665, "token_usage": {"avg_input_tokens": 1965.7411694489015, "avg_output_tokens": 427.8888888888889, "total_tokens": 215426.70525040114}, "task_count": 90}, "long": {"prompt_length": "long", "accuracy": 0.9222222222222223, "avg_time": 23.721, "avg_cost": 0.0, "avg_confidence": 0.5866666666666667, "token_usage": {"avg_input_tokens": 2787.988888888889, "avg_output_tokens": 427.8888888888889, "total_tokens": 289429.0}, "task_count": 90}}, "manufacturing_metrics": {"data_quality_handling": {"Q0": {"accuracy": 1.0, "avg_confidence": 0.6933333333333334, "task_count": 45, "error_detection_rate": 1.0}, "Q1": {"accuracy": 1.0, "avg_confidence": 0.5866666666666666, "task_count": 15, "error_detection_rate": 1.0}, "Q2": {"accuracy": 0.6666666666666666, "avg_confidence": 0.42666666666666664, "task_count": 15, "error_detection_rate": 1.0}, "Q3": {"accuracy": 0.8666666666666667, "avg_confidence": 0.42666666666666664, "task_count": 15, "error_detection_rate": 1.0}}, "task_complexity_performance": {"easy": {"accuracy": 0.7666666666666667, "avg_time": 33.93033333333333, "avg_cost": 0.0, "task_count": 30}, "medium": {"accuracy": 1.0, "avg_time": 18.90933333333333, "avg_cost": 0.0, "task_count": 30}, "hard": {"accuracy": 1.0, "avg_time": 18.323333333333334, "avg_cost": 0.0, "task_count": 30}}, "error_detection_capabilities": {}, "cross_system_validation": {"overall_integration_success": 0.9222222222222223, "data_reconciliation_quality": 0.5866666666666667, "system_complexity_handling": {"single_system_tasks": 0.7666666666666667, "multi_system_tasks": 1.0}}, "manufacturing_domain_accuracy": {"gear_identification": {"accuracy": 0.7666666666666667, "avg_confidence": 0.16000000000000003, "task_count": 30}, "printer_analysis": {"accuracy": 1.0, "avg_confidence": 0.8000000000000003, "task_count": 30}, "compliance_verification": {"accuracy": 1.0, "avg_confidence": 0.8000000000000003, "task_count": 30}}}, "recommendations": {"data_quality": "\n            Weakest Performance on: Q2 conditions (66.7% accuracy)\n            Recommendation: Implement additional data validation and error handling for Q2 scenarios\n            Consider prompt engineering improvements for data quality issue detection\n            ", "task_complexity": "\n            Most Challenging Tasks: easy (76.7% accuracy)\n            Recommendation: Consider task decomposition or specialized prompting for easy tasks\n            Average time for easy tasks: 33.9s\n            ", "manufacturing_domain": "\n            Weakest Manufacturing Domain: gear_identification (76.7% accuracy)\n            Recommendation: Develop domain-specific training or fine-tuning for gear_identification\n            Consider adding more context or examples for this task type\n            "}}