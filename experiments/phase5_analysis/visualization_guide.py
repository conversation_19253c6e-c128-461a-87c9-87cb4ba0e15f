#!/usr/bin/env python3
"""
Visualization Guide for Phase 5 Analysis

This script provides descriptions and insights for all generated visualizations
in the Phase 5 human vs LLM comparative analysis.
"""

import sys
from pathlib import Path

def display_visualization_guide(viz_dir: str = "experiments/phase5_analysis/visualizations"):
    """Display comprehensive guide to all Phase 5 visualizations"""
    
    viz_path = Path(viz_dir)
    
    if not viz_path.exists():
        print(f"❌ Visualization directory not found: {viz_path}")
        print("Please run Phase 5 analysis first: python run_phase5_analysis.py")
        return
    
    print("📊 PHASE 5 VISUALIZATION GUIDE")
    print("=" * 80)
    print("This guide explains all visualizations generated by the Phase 5 analysis.")
    print("Each visualization provides unique insights into human vs LLM performance.")
    print()
    
    # Define visualization descriptions
    visualizations = {
        "enhanced_model_comparison_dashboard.png": {
            "title": "🎯 Enhanced Model Comparison Dashboard",
            "description": "Comprehensive overview of all model performance metrics",
            "components": [
                "• Accuracy vs Speed scatter plot with statistical significance bubbles",
                "• Model accuracy ranking with human baseline comparison", 
                "• Speed improvement ranking across all models",
                "• Statistical significance matrix (accuracy & time tests)",
                "• Performance radar chart for top 3 models",
                "• Summary table with recommendations for each model"
            ],
            "insights": [
                "Identifies best overall performers balancing accuracy and speed",
                "Shows which models have statistically significant improvements",
                "Provides clear recommendations for different use cases"
            ]
        },
        
        "complexity_performance_matrix.png": {
            "title": "🧩 Task Complexity Performance Matrix", 
            "description": "Detailed analysis of model performance across easy/medium/hard tasks",
            "components": [
                "• Accuracy heatmap by model and complexity level",
                "• Human vs model comparison across complexity levels",
                "• Accuracy improvement heatmap over human baseline",
                "• Speed improvement matrix by complexity",
                "• Best model identification for each complexity level",
                "• Analysis of models struggling with specific complexities"
            ],
            "insights": [
                "Reveals which models excel at different task difficulties",
                "Identifies complexity-specific strengths and weaknesses",
                "Shows if models maintain performance across difficulty levels"
            ]
        },
        
        "quality_robustness_analysis.png": {
            "title": "🛡️ Data Quality Robustness Analysis",
            "description": "Comprehensive analysis of model performance under data corruption",
            "components": [
                "• Accuracy heatmap across Q0-Q3 quality conditions",
                "• Robustness score ranking (corrupted vs normal baseline data ratio)",
                "• Quality degradation pattern analysis",
                "• Perfect vs corrupted data performance scatter plot",
                "• Best model identification for each quality condition",
                "• Quality impact analysis and robustness vs accuracy trade-offs"
            ],
            "insights": [
                "Identifies most robust models for poor data quality environments",
                "Shows how different corruption types affect each model",
                "Reveals trade-offs between accuracy and robustness"
            ]
        },
        
        "model_specific_comparison.png": {
            "title": "🔍 Model-Specific Performance Comparison",
            "description": "Individual model analysis with statistical validation",
            "components": [
                "• Accuracy comparison with human baseline indicators",
                "• Speed improvement factors for each model",
                "• Statistical significance heatmap",
                "• Accuracy difference from human baseline"
            ],
            "insights": [
                "Provides detailed view of each model's strengths",
                "Shows statistical reliability of performance differences",
                "Identifies models that significantly outperform humans"
            ]
        },
        
        "model_complexity_analysis.png": {
            "title": "📈 Model Complexity Analysis",
            "description": "Model performance breakdown by task complexity",
            "components": [
                "• Accuracy heatmap by model and complexity",
                "• Accuracy improvement over humans by complexity",
                "• Speed improvement analysis by complexity",
                "• Best model identification for each complexity level"
            ],
            "insights": [
                "Shows which models handle complex tasks better",
                "Reveals complexity-specific performance patterns",
                "Identifies optimal models for different task difficulties"
            ]
        },
        
        "model_quality_analysis.png": {
            "title": "🔍 Model Quality Analysis",
            "description": "Model performance across data quality conditions",
            "components": [
                "• Accuracy heatmap across quality conditions",
                "• Robustness analysis and degradation patterns",
                "• Quality-specific performance comparisons",
                "• Best model identification by quality condition"
            ],
            "insights": [
                "Identifies models best suited for corrupted data",
                "Shows how data quality affects each model differently",
                "Reveals most reliable models across quality conditions"
            ]
        },
        
        "overall_performance_comparison.png": {
            "title": "📊 Overall Performance Comparison",
            "description": "High-level human vs LLM performance overview",
            "components": [
                "• Overall accuracy comparison",
                "• Average completion time comparison", 
                "• Cost per task analysis",
                "• Efficiency metrics (speedup and cost ratios)"
            ],
            "insights": [
                "Provides executive summary of LLM vs human performance",
                "Shows overall cost-effectiveness of LLM deployment",
                "Demonstrates speed and efficiency improvements"
            ]
        },
        
        "performance_by_complexity.png": {
            "title": "🎯 Performance by Complexity",
            "description": "Aggregated performance analysis across task complexity levels",
            "components": [
                "• Accuracy comparison by complexity (human vs LLM average)",
                "• Time comparison by complexity level"
            ],
            "insights": [
                "Shows how complexity affects human vs LLM performance",
                "Identifies complexity levels where LLMs excel most",
                "Reveals performance gaps across difficulty levels"
            ]
        },
        
        "performance_by_quality.png": {
            "title": "🔍 Performance by Quality",
            "description": "Aggregated performance analysis across data quality conditions",
            "components": [
                "• Accuracy comparison by quality condition",
                "• Time comparison across quality levels"
            ],
            "insights": [
                "Shows impact of data quality on performance",
                "Demonstrates LLM robustness to data corruption",
                "Identifies quality conditions where LLMs struggle"
            ]
        },
        
        "time_vs_accuracy_scatter.png": {
            "title": "⚡ Time vs Accuracy Trade-off",
            "description": "Analysis of speed vs accuracy trade-offs",
            "components": [
                "• Scatter plot of completion time vs accuracy",
                "• Human vs LLM performance points",
                "• Trend lines showing performance relationships"
            ],
            "insights": [
                "Reveals trade-offs between speed and accuracy",
                "Shows optimal performance regions",
                "Identifies models with best speed-accuracy balance"
            ]
        },
        
        "cost_analysis.png": {
            "title": "💰 Cost Analysis",
            "description": "Cost-effectiveness comparison between humans and LLMs",
            "components": [
                "• Cost per task comparison",
                "• Cost per correct answer analysis",
                "• Time vs cost relationship",
                "• Return on investment calculations"
            ],
            "insights": [
                "Demonstrates economic benefits of LLM deployment",
                "Shows cost efficiency across different metrics",
                "Provides ROI analysis for decision making"
            ]
        }
    }
    
    # Display guide for each visualization
    for filename, info in visualizations.items():
        viz_file = viz_path / filename
        
        if viz_file.exists():
            print(f"{info['title']}")
            print("─" * 60)
            print(f"📁 File: {filename}")
            print(f"📝 Description: {info['description']}")
            print()
            
            print("🔧 Components:")
            for component in info['components']:
                print(f"   {component}")
            print()
            
            print("💡 Key Insights:")
            for insight in info['insights']:
                print(f"   • {insight}")
            print()
            print("=" * 80)
            print()
        else:
            print(f"⚠️  {info['title']} - File not found: {filename}")
            print()
    
    # Usage recommendations
    print("🚀 VISUALIZATION USAGE RECOMMENDATIONS")
    print("=" * 80)
    print()
    
    print("📋 FOR EXECUTIVE PRESENTATIONS:")
    print("   • enhanced_model_comparison_dashboard.png - Complete overview")
    print("   • overall_performance_comparison.png - High-level summary")
    print("   • cost_analysis.png - Economic justification")
    print()
    
    print("🔬 FOR TECHNICAL ANALYSIS:")
    print("   • complexity_performance_matrix.png - Detailed complexity analysis")
    print("   • quality_robustness_analysis.png - Data quality robustness")
    print("   • model_specific_comparison.png - Individual model evaluation")
    print()
    
    print("🎯 FOR SPECIFIC USE CASES:")
    print("   • complexity_performance_matrix.png - Task difficulty requirements")
    print("   • quality_robustness_analysis.png - Poor data quality environments")
    print("   • time_vs_accuracy_scatter.png - Speed vs accuracy trade-offs")
    print()
    
    print("📊 FOR RESEARCH PUBLICATIONS:")
    print("   • All visualizations provide publication-quality figures")
    print("   • Statistical significance clearly indicated")
    print("   • Comprehensive methodology validation")
    print()
    
    print("💡 QUICK ACCESS COMMANDS:")
    print("   • View all results: ls experiments/phase5_analysis/visualizations/")
    print("   • Open specific chart: open experiments/phase5_analysis/visualizations/[filename]")
    print("   • Generate new analysis: python run_phase5_analysis.py")
    print("   • Get model rankings: python experiments/phase5_analysis/model_ranking_summary.py")
    print("   • Get quick insights: python experiments/phase5_analysis/quick_insights.py")
    print()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        display_visualization_guide(sys.argv[1])
    else:
        display_visualization_guide()
