# LLM Provider Configuration - Performance Optimized Selection
llm_providers:
  openai:
    api_key_env: "OPENAI_API_KEY"
    base_url: "https://api.openai.com/v1/"
    models:

      o4-mini-2025-04-16:
        max_tokens: 200000
        temperature: 0.1
        cost_per_1m_tokens_input: 1.1   # Example value
        cost_per_1m_tokens_output: 4.4 # Example value
        recommended: true
      
      gpt-4o-mini-2024-07-18:
        max_tokens: 128000
        temperature: 0.1
        cost_per_1m_tokens_input: 0.15   # Example value
        cost_per_1m_tokens_output: 0.60  # Example value
        recommended: true

    
  anthropic:
    api_key_env: "ANTHROPIC_API_KEY"
    base_url: "https://api.anthropic.com/v1/"
    models:

      claude-sonnet-4-20250514:
        max_tokens: 200000
        temperature: 0.1
        cost_per_1m_tokens_input: 3   # Example value
        cost_per_1m_tokens_output: 15 # Example value
        recommended: true
        
      claude-3-5-haiku-latest:
        max_tokens: 200000
        temperature: 0.1
        cost_per_1m_tokens_input: 3   # Example value
        cost_per_1m_tokens_output: 15 # Example value
        recommended: true

  deepseek:
    api_key_env: "DEEPSEEK_API_KEY"
    base_url: "https://api.deepseek.com"
    models:

      deepseek-reasoner:
        max_tokens: 200000
        temperature: 0.1
        cost_per_1m_tokens_input: 0.55   # Example value
        cost_per_1m_tokens_output: 2.19 # Example value
        recommended: true

      deepseek-chat:
        max_tokens: 200000
        temperature: 0.1
        cost_per_1m_tokens_input: 0.07   # Example value
        cost_per_1m_tokens_output: 1.10 # Example value
        recommended: true

# Model Selection Strategy
model_selection:
  primary_models: ["o4-mini-2025-04-16", "claude-sonnet-4-20250514","deepseek-reasoner"]
  selection_criteria: "intelligence_price_ratio"
  fallback_order: ["gpt-4o-mini-2024-07-18", "claude-3-5-haiku-latest", "deepseek-chat"]
  
# Performance Thresholds
performance_thresholds:
  max_cost_per_task: 8.00  # USD
  max_response_time: 120   # seconds
  
# Rate Limiting
rate_limits:
  openai:
    requests_per_minute: 500
    tokens_per_minute: 200000
  anthropic:
    requests_per_minute: 400
    tokens_per_minute: 100000
  deepseek:
    requests_per_minute: 600
    tokens_per_minute: 300000

# Retry Configuration
retry_config:
  max_retries: 3
  backoff_factor: 2
  timeout_seconds: 60